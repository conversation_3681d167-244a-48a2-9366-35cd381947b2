import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 点赞数据类型定义
interface LikeData {
  logId: string;
  userId: string;
}

// 创建点赞
export async function POST(req: NextRequest) {
  try {
    const { logId, userId }: LikeData = await req.json();

    // 参数验证
    if (!logId) {
      return NextResponse.json({ error: "缺少logId参数" }, { status: 400 });
    }

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 验证日志记录存在
    const userLog = await prisma.userLogs.findUnique({
      where: { id: logId },
    });

    if (!userLog) {
      return NextResponse.json({ error: "日志记录不存在" }, { status: 404 });
    }

    // 检查是否已经点赞（防重复点赞）
    const existingLike = await prisma.likes.findUnique({
      where: {
        logId_userId: {
          logId,
          userId,
        },
      },
    });

    if (existingLike) {
      return NextResponse.json({ error: "已经点赞过此日志" }, { status: 400 });
    }

    // 使用事务创建点赞记录并更新日志的点赞数组
    const result = await prisma.$transaction(async (tx) => {
      // 创建点赞记录
      const like = await tx.likes.create({
        data: {
          logId,
          userId,
        },
        include: {
          user: {
            select: {
              userId: true,
              nickname: true,
              avatarURL: true,
            },
          },
        },
      });

      // 获取当前点赞列表
      const currentLog = await tx.userLogs.findUnique({
        where: { id: logId },
        select: { likedBy: true },
      });

      // 更新日志的点赞用户ID数组
      const currentLikedBy = Array.isArray(currentLog?.likedBy)
        ? (currentLog.likedBy as string[])
        : [];

      const updatedLikedBy = [...currentLikedBy, like.id];

      await tx.userLogs.update({
        where: { id: logId },
        data: {
          likedBy: updatedLikedBy,
        },
      });

      return like;
    });

    return NextResponse.json(
      {
        success: true,
        message: "点赞成功",
        data: result,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("创建点赞失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 删除点赞
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const logId = searchParams.get("logId");
    const userId = searchParams.get("userId");

    // 参数验证
    if (!logId) {
      return NextResponse.json({ error: "缺少logId参数" }, { status: 400 });
    }

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证点赞记录存在
    const existingLike = await prisma.likes.findUnique({
      where: {
        logId_userId: {
          logId,
          userId,
        },
      },
    });

    if (!existingLike) {
      return NextResponse.json({ error: "点赞记录不存在" }, { status: 404 });
    }

    // 使用事务删除点赞记录并更新日志的点赞数组
    await prisma.$transaction(async (tx) => {
      // 删除点赞记录
      await tx.likes.delete({
        where: {
          logId_userId: {
            logId,
            userId,
          },
        },
      });

      // 获取当前点赞列表
      const currentLog = await tx.userLogs.findUnique({
        where: { id: logId },
        select: { likedBy: true },
      });

      // 更新日志的点赞用户ID数组（移除当前点赞ID）
      const currentLikedBy = Array.isArray(currentLog?.likedBy)
        ? (currentLog.likedBy as string[])
        : [];

      const updatedLikedBy = currentLikedBy.filter(
        (id) => id !== existingLike.id,
      );

      await tx.userLogs.update({
        where: { id: logId },
        data: {
          likedBy: updatedLikedBy,
        },
      });
    });

    return NextResponse.json({
      success: true,
      message: "取消点赞成功",
    });
  } catch (error) {
    console.error("删除点赞失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
