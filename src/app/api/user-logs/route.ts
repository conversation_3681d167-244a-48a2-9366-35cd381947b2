import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 日志数据类型定义
interface UserLogData {
  userId: string;
  recordType: "location" | "trip" | "recognition";
  recordId: string;
  imageList?: string[];
  description?: string;
  isPublic?: boolean;
}

// 查询参数类型定义（用于GET请求）
interface QueryParams {
  userId: string;
  recordType?: "location" | "trip" | "recognition";
  isPublic?: boolean;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

// 更新参数类型定义
interface UpdateParams {
  logId: string;
  imageList?: string[];
  description?: string;
  isPublic?: boolean;
}

// 参数验证函数
function validateRecordType(recordType: string): boolean {
  return ["location", "trip", "recognition"].includes(recordType);
}

function validateImageList(imageList: any): boolean {
  if (!Array.isArray(imageList)) return false;
  return imageList.every((url) => typeof url === "string" && url.length > 0);
}

// 创建用户日志
export async function POST(req: NextRequest) {
  try {
    const {
      userId,
      recordType,
      recordId,
      imageList = [],
      description = "",
      isPublic = false,
    }: UserLogData = await req.json();

    // 参数验证
    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    if (!recordType) {
      return NextResponse.json(
        { error: "缺少recordType参数" },
        { status: 400 },
      );
    }

    if (!validateRecordType(recordType)) {
      return NextResponse.json(
        { error: "recordType必须是: location, trip, recognition" },
        { status: 400 },
      );
    }

    if (!recordId) {
      return NextResponse.json({ error: "缺少recordId参数" }, { status: 400 });
    }

    if (imageList && !validateImageList(imageList)) {
      return NextResponse.json(
        { error: "imageList必须是字符串数组" },
        { status: 400 },
      );
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 验证关联记录存在
    let relatedRecord = null;
    const relationData: any = {};

    switch (recordType) {
      case "location":
        relatedRecord = await prisma.locationCheckIns.findUnique({
          where: { id: recordId },
        });
        if (relatedRecord) {
          relationData.locationCheckInsId = recordId;
        }
        break;
      case "trip":
        relatedRecord = await prisma.userFootprints.findUnique({
          where: { id: recordId },
        });
        if (relatedRecord) {
          relationData.userFootprintsId = recordId;
        }
        break;
      case "recognition":
        relatedRecord = await prisma.itemCard.findUnique({
          where: { id: recordId },
        });
        if (relatedRecord) {
          relationData.itemCardId = recordId;
        }
        break;
    }

    if (!relatedRecord) {
      return NextResponse.json(
        { error: `关联的${recordType}记录不存在` },
        { status: 404 },
      );
    }

    // 创建日志记录
    const userLog = await prisma.userLogs.create({
      data: {
        userId,
        recordType,
        recordId,
        imageList: imageList || [],
        description: description || "",
        isPublic: isPublic ?? false,
        ...relationData,
      },
      include: {
        user: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
        likes: {
          include: {
            user: {
              select: {
                userId: true,
                nickname: true,
                avatarURL: true,
              },
            },
          },
        },
        comments: {
          include: {
            user: {
              select: {
                userId: true,
                nickname: true,
                avatarURL: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
        LocationCheckIns: {
          include: {
            rewardAttributeLog: true,
          },
        },
        UserFootprints: {
          include: {
            completionRewardLog: true,
          },
        },
        ItemCard: {
          include: {
            creationRewardLog: true,
          },
        },
      },
    });

    return NextResponse.json(
      {
        success: true,
        message: "用户日志创建成功",
        data: userLog,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("创建用户日志失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 查询用户日志
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const recordType = searchParams.get("recordType") as
      | "location"
      | "trip"
      | "recognition"
      | null;
    const isPublic = searchParams.get("isPublic");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    // 参数验证
    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    if (recordType && !validateRecordType(recordType)) {
      return NextResponse.json(
        { error: "recordType必须是: location, trip, recognition" },
        { status: 400 },
      );
    }

    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: "页码必须大于0，每页数量必须在1-100之间" },
        { status: 400 },
      );
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 构建查询条件
    const whereCondition: any = {
      userId,
    };

    if (recordType) {
      whereCondition.recordType = recordType;
    }

    if (isPublic !== null) {
      whereCondition.isPublic = isPublic === "true";
    }

    // 添加时间范围过滤
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt.lte = new Date(endDate);
      }
    }

    // 计算跳过的记录数
    const skip = (page - 1) * limit;

    // 查询日志记录和总数
    const [userLogs, total] = await Promise.all([
      prisma.userLogs.findMany({
        where: whereCondition,
        include: {
          user: {
            select: {
              userId: true,
              nickname: true,
              avatarURL: true,
            },
          },
          likes: {
            include: {
              user: {
                select: {
                  userId: true,
                  nickname: true,
                  avatarURL: true,
                },
              },
            },
          },
          comments: {
            include: {
              user: {
                select: {
                  userId: true,
                  nickname: true,
                  avatarURL: true,
                },
              },
            },
            orderBy: {
              createdAt: "asc",
            },
          },
          LocationCheckIns: {
            include: {
              rewardAttributeLog: true,
              userFootprints: true,
            },
          },
          UserFootprints: {
            include: {
              completionRewardLog: true,
            },
          },
          ItemCard: {
            include: {
              creationRewardLog: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.userLogs.count({
        where: whereCondition,
      }),
    ]);

    // 对于recognition类型的日志，需要获取对应的CardAcquisitionRecord
    const processedLogs = await Promise.all(
      userLogs.map(async (log) => {
        if (log.recordType === "recognition" && log.ItemCard) {
          // 查找对应的CardAcquisitionRecord
          const acquisitionRecord =
            await prisma.cardAcquisitionRecord.findFirst({
              where: {
                userId: log.userId,
                cardId: log.ItemCard.id,
              },
              include: {
                card: true,
              },
            });

          // 返回包含CardAcquisitionRecord的日志
          return {
            ...log,
            CardAcquisitionRecord: acquisitionRecord,
          };
        }
        return log;
      }),
    );

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        logs: processedLogs,
        pagination: {
          current: page,
          total: totalPages,
          count: total,
          limit,
          hasNext: hasNextPage,
          hasPrev: hasPrevPage,
        },
      },
    });
  } catch (error) {
    console.error("查询用户日志失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 更新用户日志
export async function PATCH(req: NextRequest) {
  try {
    const { logId, imageList, description, isPublic }: UpdateParams =
      await req.json();

    // 参数验证
    if (!logId) {
      return NextResponse.json({ error: "缺少logId参数" }, { status: 400 });
    }

    if (imageList && !validateImageList(imageList)) {
      return NextResponse.json(
        { error: "imageList必须是字符串数组" },
        { status: 400 },
      );
    }

    // 验证日志记录存在
    const existingLog = await prisma.userLogs.findUnique({
      where: { id: logId },
    });

    if (!existingLog) {
      return NextResponse.json({ error: "日志记录不存在" }, { status: 404 });
    }

    // 构建更新数据
    const updateData: any = {};

    if (imageList !== undefined) {
      updateData.imageList = imageList;
    }

    if (description !== undefined) {
      updateData.description = description;
    }

    if (isPublic !== undefined) {
      updateData.isPublic = isPublic;
    }

    // 如果没有要更新的字段
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: "至少需要提供一个要更新的字段" },
        { status: 400 },
      );
    }

    // 执行更新
    const updatedLog = await prisma.userLogs.update({
      where: { id: logId },
      data: updateData,
      include: {
        user: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
        likes: {
          include: {
            user: {
              select: {
                userId: true,
                nickname: true,
                avatarURL: true,
              },
            },
          },
        },
        comments: {
          include: {
            user: {
              select: {
                userId: true,
                nickname: true,
                avatarURL: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
        LocationCheckIns: true,
        UserFootprints: true,
        ItemCard: true,
      },
    });

    return NextResponse.json({
      success: true,
      message: "日志更新成功",
      data: updatedLog,
    });
  } catch (error) {
    console.error("更新用户日志失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 删除用户日志
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const logId = searchParams.get("logId");
    const userId = searchParams.get("userId");

    // 参数验证
    if (!logId) {
      return NextResponse.json({ error: "缺少logId参数" }, { status: 400 });
    }

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证日志记录存在且属于该用户
    const existingLog = await prisma.userLogs.findUnique({
      where: { id: logId },
    });

    if (!existingLog) {
      return NextResponse.json({ error: "日志记录不存在" }, { status: 404 });
    }

    if (existingLog.userId !== userId) {
      return NextResponse.json(
        { error: "无权限删除此日志记录" },
        { status: 403 },
      );
    }

    // 删除日志记录（关联的点赞和评论会通过外键约束自动删除）
    await prisma.userLogs.delete({
      where: { id: logId },
    });

    return NextResponse.json({
      success: true,
      message: "日志删除成功",
    });
  } catch (error) {
    console.error("删除用户日志失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
