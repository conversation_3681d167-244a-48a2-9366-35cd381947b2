import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 查询指定ID的足迹记录详情
export async function POST(req: NextRequest) {
  try {
    const { footprintId } = await req.json();

    if (!footprintId) {
      return NextResponse.json({ error: "缺少足迹记录ID" }, { status: 400 });
    }

    // 查询足迹记录，包含关联的地点打卡记录
    const footprint = await prisma.userFootprints.findUnique({
      where: { id: footprintId },
      include: {
        user: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
        locationCheckIns: {
          select: {
            id: true,
            position: true,
            latitude: true,
            longitude: true,
            photoURLs: true,
            description: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: {
            createdAt: "asc", // 按时间顺序排列地点打卡
          },
        },
      },
    });

    if (!footprint) {
      return NextResponse.json({ error: "足迹记录不存在" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: footprint,
    });
  } catch (error) {
    console.error("查询足迹记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
