import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  getMonthUtcRange,
  isValidMonth,
  getCurrentBeijingMonth,
} from "@/lib/timeUtils";

/**
 * 结构组成区域API
 * GET /api/eco/structure?userId=...&month=YYYY-MM
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const month = searchParams.get("month") || getCurrentBeijingMonth();

    // 参数验证
    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    if (!isValidMonth(month)) {
      return NextResponse.json(
        {
          error: "month参数格式错误，应为YYYY-MM格式",
        },
        { status: 400 },
      );
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
      select: { userId: true },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 获取月份的UTC时间范围
    const { start: monthStart, end: monthEnd } = getMonthUtcRange(month);

    // 并行查询各种结构数据
    const [transportData, cardsData, shoppingCardsData] = await Promise.all([
      // 交通出行结构
      getTransportStructure(userId, monthStart, monthEnd),

      // 卡片创建结构
      getCardsStructure(userId, monthStart, monthEnd),

      // 购物卡分数区间结构
      getShoppingScoreDistribution(userId, monthStart, monthEnd),
    ]);

    const response = {
      userId,
      month,
      transport: transportData,
      cards: cardsData,
      shoppingScoreDist: shoppingCardsData,
    };

    return NextResponse.json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error("获取结构组成数据失败:", error);
    return NextResponse.json(
      {
        error: "服务器内部错误",
      },
      { status: 500 },
    );
  }
}

/**
 * 获取交通出行结构数据
 */
async function getTransportStructure(
  userId: string,
  monthStart: Date,
  monthEnd: Date,
) {
  const footprints = await prisma.userFootprints.groupBy({
    by: ["activityType"],
    where: {
      userId,
      isFinished: true,
      updatedAt: { gte: monthStart, lte: monthEnd },
    },
    _count: {
      id: true,
    },
  });

  const counts = {
    walking: 0,
    cycling: 0,
    bus: 0,
    subway: 0,
  };

  // 统计各种交通方式的数量
  footprints.forEach((fp) => {
    if (fp.activityType in counts) {
      counts[fp.activityType as keyof typeof counts] = fp._count.id;
    }
  });

  const total = Object.values(counts).reduce((sum, count) => sum + count, 0);

  // 计算占比
  const ratio = {
    walking: total > 0 ? Math.round((counts.walking / total) * 1000) / 1000 : 0,
    cycling: total > 0 ? Math.round((counts.cycling / total) * 1000) / 1000 : 0,
    bus: total > 0 ? Math.round((counts.bus / total) * 1000) / 1000 : 0,
    subway: total > 0 ? Math.round((counts.subway / total) * 1000) / 1000 : 0,
  };

  return { counts, ratio };
}

/**
 * 获取卡片创建结构数据
 */
async function getCardsStructure(
  userId: string,
  monthStart: Date,
  monthEnd: Date,
) {
  const cards = await prisma.itemCard.groupBy({
    by: ["cardType"],
    where: {
      authorId: userId,
      createdAt: { gte: monthStart, lte: monthEnd },
    },
    _count: {
      id: true,
    },
  });

  const counts = {
    scenery: 0,
    shopping: 0,
  };

  // 统计各种卡片类型的数量
  cards.forEach((card) => {
    if (card.cardType in counts) {
      counts[card.cardType as keyof typeof counts] = card._count.id;
    }
  });

  const total = Object.values(counts).reduce((sum, count) => sum + count, 0);

  // 计算占比
  const ratio = {
    scenery: total > 0 ? Math.round((counts.scenery / total) * 1000) / 1000 : 0,
    shopping:
      total > 0 ? Math.round((counts.shopping / total) * 1000) / 1000 : 0,
  };

  return { counts, ratio };
}

/**
 * 获取购物卡分数区间分布数据
 */
async function getShoppingScoreDistribution(
  userId: string,
  monthStart: Date,
  monthEnd: Date,
) {
  const shoppingCards = await prisma.itemCard.findMany({
    where: {
      authorId: userId,
      cardType: "shopping",
      createdAt: { gte: monthStart, lte: monthEnd },
    },
    select: {
      structureScore: true,
      materialScore: true,
      packagingScore: true,
    },
  });

  const counts = {
    low: 0, // [0, 60)
    mid: 0, // [60, 80)
    high: 0, // [80, 100]
  };

  // 计算每张卡片的平均分数并分类
  shoppingCards.forEach((card) => {
    const scores = [
      card.structureScore || 0,
      card.materialScore || 0,
      card.packagingScore || 0,
    ];
    const avgScore = scores.reduce((sum, score) => sum + score, 0) / 3;

    if (avgScore < 60) {
      counts.low++;
    } else if (avgScore < 80) {
      counts.mid++;
    } else {
      counts.high++;
    }
  });

  const total = Object.values(counts).reduce((sum, count) => sum + count, 0);

  // 计算占比
  const ratio = {
    low: total > 0 ? Math.round((counts.low / total) * 1000) / 1000 : 0,
    mid: total > 0 ? Math.round((counts.mid / total) * 1000) / 1000 : 0,
    high: total > 0 ? Math.round((counts.high / total) * 1000) / 1000 : 0,
  };

  return { counts, ratio };
}
