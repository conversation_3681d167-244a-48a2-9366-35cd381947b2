import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  getMonthUtcRange,
  isValidMonth,
  getCurrentBeijingMonth,
} from "@/lib/timeUtils";
import dayjs from "dayjs";

/**
 * 日历API
 * GET /api/eco/calendar?userId=...&month=YYYY-MM
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const month = searchParams.get("month") || getCurrentBeijingMonth();

    // 参数验证
    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    if (!isValidMonth(month)) {
      return NextResponse.json(
        {
          error: "month参数格式错误，应为YYYY-MM格式",
        },
        { status: 400 },
      );
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
      select: { userId: true },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 获取月份的UTC时间范围（暂时不需要，但保留以备后用）
    // const { start: monthStart, end: monthEnd } = getMonthUtcRange(month);

    // 查询该月的所有UserEcoDaily数据
    const ecoData = await prisma.userEcoDaily.findMany({
      where: {
        userId,
        dateLocal: {
          gte: dayjs(`${month}-01`).toDate(),
          lte: dayjs(`${month}-01`).endOf("month").toDate(),
        },
      },
      select: {
        dateLocal: true,
        hasCard: true,
        hasCheckin: true,
        hasFootprint: true,
        co2SavedKg: true,
        logsCount: true,
        cardCount: true,
        checkinCount: true,
        tripCount: true,
      },
      orderBy: {
        dateLocal: "asc",
      },
    });

    // 生成该月的所有日期
    const monthStartDay = dayjs(`${month}-01`);
    const monthEndDay = monthStartDay.endOf("month");
    const daysInMonth = monthEndDay.date();

    const days = [];

    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = monthStartDay.date(day).format("YYYY-MM-DD");

      // 查找对应的生态数据
      const dayData = ecoData.find(
        (data) => dayjs(data.dateLocal).format("YYYY-MM-DD") === dateStr,
      );

      days.push({
        date: dateStr,
        hasCard: dayData?.hasCard || false,
        hasCheckin: dayData?.hasCheckin || false,
        hasFootprint: dayData?.hasFootprint || false,
        co2SavedKg: Math.round((dayData?.co2SavedKg || 0) * 100) / 100, // 保留两位小数
        counts: {
          logs: dayData?.logsCount || 0,
          trip: dayData?.tripCount || 0,
          checkin: dayData?.checkinCount || 0,
          card: dayData?.cardCount || 0,
        },
      });
    }

    const response = {
      userId,
      month,
      days,
    };

    return NextResponse.json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error("获取日历数据失败:", error);
    return NextResponse.json(
      {
        error: "服务器内部错误",
      },
      { status: 500 },
    );
  }
}
