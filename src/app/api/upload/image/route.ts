import { NextResponse } from "next/server";
import COS from "cos-nodejs-sdk-v5";

// 腾讯云 COS 配置
const cos = new COS({
  SecretId: process.env.TENCENT_SECRET_ID!, // 从环境变量获取
  SecretKey: process.env.TENCENT_SECRET_KEY!, // 从环境变量获取
});

// COS 存储桶配置
const BUCKET = process.env.COS_BUCKET!; // 存储桶名称，格式：examplebucket-1250000000
const REGION = process.env.COS_REGION!; // 存储桶地域，如：ap-beijing

// 图片上传接口
export async function POST(req: Request) {
  try {
    // 解析 multipart/form-data
    const formData = await req.formData();
    const imageFile = formData.get("image") as File;

    if (!imageFile) {
      return NextResponse.json(
        { success: false, message: "未找到图片文件" },
        { status: 400 },
      );
    }

    // 验证文件类型
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(imageFile.type)) {
      return NextResponse.json(
        { success: false, message: "不支持的图片格式，仅支持 JPEG、PNG、WebP" },
        { status: 400 },
      );
    }

    // 验证文件大小（限制为 10MB）
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (imageFile.size > maxSize) {
      return NextResponse.json(
        { success: false, message: "图片文件过大，最大支持 10MB" },
        { status: 400 },
      );
    }

    // 生成唯一文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 15);
    const fileExtension = imageFile.name.split(".").pop() || "jpg";
    const filename = `images/${timestamp}_${randomStr}.${fileExtension}`;

    // 将 File 转换为 Buffer
    const arrayBuffer = await imageFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // 上传到腾讯云 COS
    await new Promise<any>((resolve, reject) => {
      cos.putObject(
        {
          Bucket: BUCKET,
          Region: REGION,
          Key: filename,
          Body: buffer,
          ContentType: imageFile.type,
          StorageClass: "STANDARD",
        },
        (err, data) => {
          if (err) {
            console.error("COS 上传失败:", err);
            reject(err);
          } else {
            resolve(data);
          }
        },
      );
    });

    // 构建完整的访问 URL
    const imageURL = `https://${BUCKET}.cos.${REGION}.myqcloud.com/${filename}`;

    return NextResponse.json({
      success: true,
      message: "图片上传成功",
      data: {
        url: imageURL,
        filename: filename,
        originalName: imageFile.name,
        size: imageFile.size,
        type: imageFile.type,
      },
    });
  } catch (error) {
    console.error("图片上传失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "图片上传失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}

// 图片删除接口
export async function DELETE(req: Request) {
  try {
    // 从请求 body 解析 JSON，获取 filename
    const { filename } = await req.json();

    if (!filename) {
      return NextResponse.json(
        { success: false, message: "缺少文件名 filename 参数" },
        { status: 400 },
      );
    }

    // 获取合适的文件路径
    const prefix = `https://${BUCKET}.cos.${REGION}.myqcloud.com/`;
    const key = filename.startsWith("http")
      ? filename.replace(prefix, "")
      : filename; // 如果本来就是 images/xxx.jpg，直接使用

    // 调用 COS 删除对象
    await new Promise<any>((resolve, reject) => {
      cos.deleteObject(
        {
          Bucket: BUCKET,
          Region: REGION,
          Key: key,
        },
        (err, data) => {
          if (err) {
            console.error("COS 删除失败:", err);
            reject(err);
          } else {
            resolve(data);
          }
        },
      );
    });

    return NextResponse.json({
      success: true,
      message: "文件删除成功",
      data: { filename },
    });
  } catch (error) {
    console.error("删除失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "文件删除失败",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 },
    );
  }
}
