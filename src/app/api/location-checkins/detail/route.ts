import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 详情查询参数类型定义
interface DetailParams {
  id: string;
}

// 查询指定地点打卡记录详情
export async function POST(req: NextRequest) {
  try {
    const { id }: DetailParams = await req.json();

    // 参数验证
    if (!id) {
      return NextResponse.json({ error: "缺少id参数" }, { status: 400 });
    }

    // 查询地点打卡记录详情，包含用户信息和关联的出行记录
    const checkIn = await prisma.locationCheckIns.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            userId: true,
            nickname: true,
            avatar: true,
            avatarURL: true,
          },
        },
        userFootprints: {
          select: {
            id: true,
            activityType: true,
            isFinished: true,
            totalDistance: true,
            createdAt: true,
          },
        },
      },
    });

    if (!checkIn) {
      return NextResponse.json(
        { error: "地点打卡记录不存在" },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      message: "查询成功",
      data: checkIn,
    });
  } catch (error) {
    console.error("查询地点打卡记录详情失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
