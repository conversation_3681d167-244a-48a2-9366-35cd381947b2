import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 查询指定用户发送给其他用户的已读情况，按照已读和未读分组返回
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 查询用户发送的所有道具交互记录
    const sentInteractions = await prisma.propInteraction.findMany({
      where: { senderUserId: userId },
      include: {
        receiver: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
      },
      orderBy: {
        interactionTime: "desc",
      },
    });

    // 按已读和未读分组
    const readInteractions = sentInteractions.filter(
      (interaction) => interaction.isRead,
    );
    const unreadInteractions = sentInteractions.filter(
      (interaction) => !interaction.isRead,
    );

    // 统计信息
    const stats = {
      total: sentInteractions.length,
      read: readInteractions.length,
      unread: unreadInteractions.length,
    };

    return NextResponse.json({
      success: true,
      message: "查询成功",
      data: {
        stats,
        readInteractions,
        unreadInteractions,
      },
    });
  } catch (error) {
    console.error("查询道具交互统计失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 },
    );
  }
}
