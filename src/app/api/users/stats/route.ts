import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  applyUserStatChanges,
  UserStatDelta,
  USER_STAT_REASONS,
  UserStatReason,
} from "@/lib/userStats";

const DEFAULT_LOG_LIMIT = 20;
const MAX_LOG_LIMIT = 100;

const USER_STAT_REASON_SET = new Set<string>(USER_STAT_REASONS);

// 解析请求体中的增量字段，限制为数字或 undefined
function parseNumericDelta(value: unknown): number | undefined {
  if (value === null || value === undefined) {
    return undefined;
  }
  const num = Number(value);
  return Number.isFinite(num) ? num : undefined;
}

// 规范化用户属性返回值，避免前端处理 Decimal 类型
function normalizeStats(user: {
  userId: string;
  carbonCoins: number;
  experiencePoints: number;
  carbonReduction: number | null;
  honorPoints: number;
}) {
  return {
    userId: user.userId,
    carbonCoins: user.carbonCoins,
    experiencePoints: user.experiencePoints,
    carbonReduction: user.carbonReduction ?? 0,
    honorPoints: user.honorPoints,
  };
}

// 格式化属性变更日志，保证减碳量字段为 number
function normalizeLog(log: {
  id: string;
  userId: string;
  carbonCoinsDelta: number;
  experiencePointsDelta: number;
  carbonReductionDelta: number | null;
  honorPointsDelta: number;
  reason: string | null;
  metadata: unknown;
  createdAt: Date;
}) {
  return {
    ...log,
    carbonReductionDelta: log.carbonReductionDelta ?? 0,
  };
}

// 判断查询参数是否需要返回日志列表
function parseIncludeLogs(value: string | null): boolean {
  if (!value) return false;
  return value === "1" || value.toLowerCase() === "true";
}

// 限制日志返回条数，防止一次拉取过多数据
function clampLogLimit(value: string | null): number {
  if (!value) return DEFAULT_LOG_LIMIT;
  const parsed = Number.parseInt(value, 10);
  if (!Number.isFinite(parsed) || parsed <= 0) {
    return DEFAULT_LOG_LIMIT;
  }
  return Math.min(parsed, MAX_LOG_LIMIT);
}

// 查询用户累计属性以及可选的属性变更日志
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    const includeLogs = parseIncludeLogs(searchParams.get("includeLogs"));
    const logLimit = clampLogLimit(searchParams.get("logLimit"));

    const user = await prisma.user.findUnique({
      where: { userId },
      select: {
        userId: true,
        carbonCoins: true,
        experiencePoints: true,
        carbonReduction: true,
        honorPoints: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    const response: {
      stats: ReturnType<typeof normalizeStats>;
      logs?: ReturnType<typeof normalizeLog>[];
    } = {
      stats: normalizeStats(user),
    };

    if (includeLogs) {
      const logs = await prisma.userAttributeLog.findMany({
        where: { userId },
        orderBy: { createdAt: "desc" },
        take: logLimit,
      });

      response.logs = logs.map(normalizeLog);
    }

    return NextResponse.json({ success: true, data: response });
  } catch (error) {
    console.error("获取用户属性失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 根据前端提交的增量更新用户属性并追加日志
export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();

    const userId: string | undefined = body?.userId;
    const carbonCoinsDelta = parseNumericDelta(body?.carbonCoins);
    const experiencePointsDelta = parseNumericDelta(body?.experiencePoints);
    const carbonReductionDelta = parseNumericDelta(body?.carbonReduction);
    const honorPointsDelta = parseNumericDelta(body?.honorPoints);
    const reason = parseReason(body?.reason);
    if (body?.reason !== undefined && reason === undefined) {
      return NextResponse.json({ error: "reason 参数不合法" }, { status: 400 });
    }
    const metadata = body?.metadata;

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    const changes: UserStatDelta = {};

    if (carbonCoinsDelta !== undefined && carbonCoinsDelta !== 0) {
      changes.carbonCoins = carbonCoinsDelta;
    }

    if (experiencePointsDelta !== undefined && experiencePointsDelta !== 0) {
      changes.experiencePoints = experiencePointsDelta;
    }

    if (carbonReductionDelta !== undefined && carbonReductionDelta !== 0) {
      changes.carbonReduction = carbonReductionDelta;
    }

    if (honorPointsDelta !== undefined && honorPointsDelta !== 0) {
      changes.honorPoints = honorPointsDelta;
    }

    if (Object.keys(changes).length === 0) {
      return NextResponse.json(
        { error: "未提供需要更新的属性" },
        { status: 400 },
      );
    }

    let normalizedMetadata = metadata as unknown;
    if (metadata !== undefined) {
      try {
        normalizedMetadata = JSON.parse(JSON.stringify(metadata));
      } catch {
        return NextResponse.json(
          { error: "metadata 必须是可序列化的 JSON 对象" },
          { status: 400 },
        );
      }
    }

    const { user: updatedUser, log } = await applyUserStatChanges(prisma, {
      userId,
      changes,
      reason,
      metadata: normalizedMetadata,
    });

    return NextResponse.json({
      success: true,
      data: {
        stats: normalizeStats(updatedUser),
        log: normalizeLog(log),
      },
    });
  } catch (error) {
    console.error("更新用户属性失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
// 校验 reason 是否在枚举列表中
function parseReason(value: unknown): UserStatReason | undefined {
  if (value === undefined || value === null) {
    return undefined;
  }
  if (typeof value !== "string") {
    return undefined;
  }
  return USER_STAT_REASON_SET.has(value)
    ? (value as UserStatReason)
    : undefined;
}
