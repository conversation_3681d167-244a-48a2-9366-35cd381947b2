/**
 * 时间工具函数
 * 处理北京时间(Asia/Shanghai)的日期转换和计算
 */

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

// 扩展dayjs插件
dayjs.extend(utc);
dayjs.extend(timezone);

// 北京时区
export const BEIJING_TIMEZONE = "Asia/Shanghai";

/**
 * 获取北京时间的当前日期（YYYY-MM-DD格式）
 */
export function getCurrentBeijingDate(): string {
  return dayjs().tz(BEIJING_TIMEZONE).format("YYYY-MM-DD");
}

/**
 * 将UTC时间转换为北京时间的日期
 * @param utcDate UTC时间
 * @returns 北京时间的日期字符串 (YYYY-MM-DD)
 */
export function utcToBeijingDate(utcDate: Date): string {
  return dayjs(utcDate).tz(BEIJING_TIMEZONE).format("YYYY-MM-DD");
}

/**
 * 将北京时间的日期字符串转换为UTC时间范围
 * @param beijingDateStr 北京时间日期字符串 (YYYY-MM-DD)
 * @returns UTC时间范围 { start: Date, end: Date }
 */
export function beijingDateToUtcRange(beijingDateStr: string): {
  start: Date;
  end: Date;
} {
  const beijingStart = dayjs.tz(`${beijingDateStr} 00:00:00`, BEIJING_TIMEZONE);
  const beijingEnd = dayjs.tz(`${beijingDateStr} 23:59:59`, BEIJING_TIMEZONE);

  return {
    start: beijingStart.utc().toDate(),
    end: beijingEnd.utc().toDate(),
  };
}

/**
 * 获取指定月份的北京时间范围
 * @param month 月份字符串 (YYYY-MM)
 * @returns UTC时间范围 { start: Date, end: Date }
 */
export function getMonthUtcRange(month: string): { start: Date; end: Date } {
  const monthStart = dayjs.tz(`${month}-01 00:00:00`, BEIJING_TIMEZONE);
  const monthEnd = monthStart.endOf("month").tz(BEIJING_TIMEZONE);

  return {
    start: monthStart.utc().toDate(),
    end: monthEnd.utc().toDate(),
  };
}

/**
 * 验证月份格式是否正确 (YYYY-MM)
 * @param month 月份字符串
 * @returns 是否有效
 */
export function isValidMonth(month: string): boolean {
  return /^\d{4}-\d{2}$/.test(month) && dayjs(month, "YYYY-MM", true).isValid();
}

/**
 * 获取当前北京时间的月份字符串 (YYYY-MM)
 */
export function getCurrentBeijingMonth(): string {
  return dayjs().tz(BEIJING_TIMEZONE).format("YYYY-MM");
}

/**
 * 计算两个日期之间的天数差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数差
 */
export function daysBetween(startDate: string, endDate: string): number {
  return dayjs(endDate).diff(dayjs(startDate), "day");
}

/**
 * 获取日期的前一天
 * @param dateStr 日期字符串 (YYYY-MM-DD)
 * @returns 前一天的日期字符串
 */
export function getPreviousDay(dateStr: string): string {
  return dayjs(dateStr).subtract(1, "day").format("YYYY-MM-DD");
}

/**
 * 获取日期的后一天
 * @param dateStr 日期字符串 (YYYY-MM-DD)
 * @returns 后一天的日期字符串
 */
export function getNextDay(dateStr: string): string {
  return dayjs(dateStr).add(1, "day").format("YYYY-MM-DD");
}
