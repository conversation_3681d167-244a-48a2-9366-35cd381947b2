import { Prisma, PrismaClient } from "../generated/prisma";

export const USER_STAT_REASONS = [
  "sign_in_reward",
  "location_check_in",
  "footprint_completed",
  "item_card_created",
] as const;

export type UserStatReason = (typeof USER_STAT_REASONS)[number];

export type UserStatDelta = {
  carbonCoins?: number;
  experiencePoints?: number;
  carbonReduction?: number;
  honorPoints?: number;
};

export interface ApplyUserStatChangesOptions {
  userId: string;
  changes: UserStatDelta;
  reason?: UserStatReason;
  metadata?: unknown;
}

export interface ApplyUserStatChangesResult {
  user: {
    userId: string;
    carbonCoins: number;
    experiencePoints: number;
    carbonReduction: number;
    honorPoints: number;
  };
  log: {
    id: string;
    userId: string;
    carbonCoinsDelta: number;
    experiencePointsDelta: number;
    carbonReductionDelta: number;
    honorPointsDelta: number;
    reason: UserStatReason | null;
    metadata: Prisma.JsonValue | null;
    createdAt: Date;
  };
}

type PrismaClientOrTx = PrismaClient | Prisma.TransactionClient;

// 判断是否存在需要更新的用户属性增量
function hasStatChange(changes: UserStatDelta): boolean {
  return (
    (changes.carbonCoins ?? 0) !== 0 ||
    (changes.experiencePoints ?? 0) !== 0 ||
    (changes.carbonReduction ?? 0) !== 0 ||
    (changes.honorPoints ?? 0) !== 0
  );
}

// 应用用户属性变更并写入日志，需传入 Prisma 客户端或事务客户端
export async function applyUserStatChanges(
  client: PrismaClientOrTx,
  { userId, changes, reason, metadata }: ApplyUserStatChangesOptions,
): Promise<ApplyUserStatChangesResult> {
  if (!hasStatChange(changes)) {
    throw new Error("No stat changes provided");
  }

  const updateData: Prisma.UserUpdateInput = {};

  if ((changes.carbonCoins ?? 0) !== 0) {
    updateData.carbonCoins = { increment: changes.carbonCoins! };
  }

  if ((changes.experiencePoints ?? 0) !== 0) {
    updateData.experiencePoints = { increment: changes.experiencePoints! };
  }

  if ((changes.carbonReduction ?? 0) !== 0) {
    updateData.carbonReduction = {
      increment: changes.carbonReduction!,
    };
  }

  if ((changes.honorPoints ?? 0) !== 0) {
    updateData.honorPoints = { increment: changes.honorPoints! };
  }

  const updated = await client.user.update({
    where: { userId },
    data: updateData,
    select: {
      userId: true,
      carbonCoins: true,
      experiencePoints: true,
      carbonReduction: true,
      honorPoints: true,
    },
  });

  let metadataValue:
    | Prisma.InputJsonValue
    | null
    | undefined
    | Prisma.NullTypes.JsonNull;
  if (metadata === undefined) {
    metadataValue = undefined;
  } else if (metadata === null) {
    metadataValue = Prisma.JsonNull;
  } else {
    try {
      metadataValue = JSON.parse(
        JSON.stringify(metadata),
      ) as Prisma.InputJsonValue;
    } catch {
      throw new Error("Metadata must be serializable to JSON");
    }
  }

  const log = await client.userAttributeLog.create({
    data: {
      userId,
      carbonCoinsDelta: changes.carbonCoins ?? 0,
      experiencePointsDelta: changes.experiencePoints ?? 0,
      carbonReductionDelta: changes.carbonReduction ?? 0,
      honorPointsDelta: changes.honorPoints ?? 0,
      reason,
      ...(metadataValue !== undefined && { metadata: metadataValue }),
    },
    select: {
      id: true,
      userId: true,
      carbonCoinsDelta: true,
      experiencePointsDelta: true,
      carbonReductionDelta: true,
      honorPointsDelta: true,
      reason: true,
      metadata: true,
      createdAt: true,
    },
  });

  return { user: updated, log };
}
