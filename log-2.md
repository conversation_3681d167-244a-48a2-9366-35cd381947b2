# API 文档

## 概述

本文档包含道具交互系统和卡片系统的 API 接口文档。

---

# 卡片系统 API 文档

## 概述

卡片系统支持创建风景和购物两种类型的卡片，每种卡片都有对应的奖励机制。用户可以创建、传输和获得卡片。

---

## 卡片系统 API 接口列表

### 1. 创建卡片

- **接口路径**: `POST /api/itemCard?userId={userId}`
- **功能**: 创建新的卡片（风景或购物类型），奖励由后端根据评分自动计算
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/itemCard?userId=alice" \
  -H "content-type: application/json" \
  -d '{"cardType":"scenery","title":"美丽的日落","description":"在海边看到的美丽日落","imageFileName":"sunset.jpg","imageURL":"https://example.com/sunset.jpg","location":"海滩","latitude":39.9042,"longitude":116.4074,"structureScore":85,"materialScore":90,"packagingScore":80,"mark":"FSC"}'
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 创建者用户ID
  }
  // Body 参数
  {
    cardType: "scenery" | "shopping"; // 卡片类型
    themeColor?: string; // 主题色（仅购物卡片需要）
    structureScore?: number; // 结构评分（0-100，可选）
    materialScore?: number; // 材料评分（0-100，可选）
    packagingScore?: number; // 包装评分（0-100，可选）
    mark?: string; // 环保标志（可选，必须是有效的环保标志才会奖励5碳币）
    title: string; // 卡片标题
    description: string; // 卡片描述
    imageFileName: string; // 本地存储的文件名
    imageURL: string; // COS返回的URL
    location: string; // 位置字符串
    latitude?: number; // 纬度
    longitude?: number; // 经度
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "卡片创建成功";
    data: {
      id: string;
      cardType: "scenery" | "shopping";
      themeColor: string | null;
      structureScore: number | null; // 结构评分
      materialScore: number | null; // 材料评分
      packagingScore: number | null; // 包装评分
      mark: string | null; // 备注标记
      title: string;
      description: string;
      imageFileName: string;
      imageURL: string;
      location: string;
      latitude: number | null;
      longitude: number | null;
      createdAt: Date;
      authorId: string;
      creationRewardLog: { // 奖励日志信息
        id: string;
        carbonCoinsDelta: number; // 实际获得的碳币
        experiencePointsDelta: number; // 实际获得的经验
        reason: "item_card_created";
        metadata: any; // 包含计算详情
        createdAt: Date;
      } | null;
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误或无效的卡片类型
  - `500`: 服务器错误

### 2. 获取用户卡片

- **接口路径**: `GET /api/userItemCard?userId={userId}`
- **功能**: 获取用户获得的所有卡片（包括创建的和接收的）
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/userItemCard?userId=alice"
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      cards: Array<{
        // 卡片基本信息
        id: string;
        title: string;
        description: string;
        cardType: "scenery" | "shopping";
        themeColor: string | null;
        // 评分信息
        structureScore: number | null;
        materialScore: number | null;
        packagingScore: number | null;
        mark: string | null;
        // 计算得出的奖励
        coinReward: number; // 根据评分计算的碳币奖励
        experienceReward: number; // 根据评分计算的经验奖励
        imageFileName: string;
        imageURL: string;
        location: string;
        latitude: number | null;
        longitude: number | null;
        createdAt: Date;
        // 作者信息
        author: {
          userId: string;
          nickname: string;
          avatarURL: string | null;
        };
        // 获得记录信息
        acquiredAt: Date; // 获取时间
        isAuthor: boolean; // 是否是作者
        acquisitionRecordId: string; // 获得记录ID
      }>;
      total: number;
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 缺少参数
  - `404`: 用户不存在
  - `500`: 服务器错误

### 3. 修改卡片信息

- **接口路径**: `PATCH /api/itemCard?userId={userId}&cardId={cardId}`
- **功能**: 修改卡片信息（只有作者可以修改）
- **测试性 Curl：**

```bash
curl -i -X PATCH "http://*************:3000/api/itemCard?userId=alice&cardId=card123" \
  -H "content-type: application/json" \
  -d '{"title":"更新的标题","description":"更新的描述","coinReward":15}'
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必须是作者）
    cardId: string; // 卡片ID
  }
  // Body 参数（所有字段都是可选的）
  {
    cardType?: "scenery" | "shopping";
    themeColor?: string;
    structureScore?: number; // 结构评分（0-100）
    materialScore?: number; // 材料评分（0-100）
    packagingScore?: number; // 包装评分（0-100）
    mark?: string; // 环保标志（必须是有效的环保标志）
    title?: string;
    description?: string;
    imageFileName?: string;
    imageURL?: string;
    location?: string;
    latitude?: number;
    longitude?: number;
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "卡片信息更新成功";
    data: {
      id: string;
      cardType: "scenery" | "shopping";
      themeColor: string | null;
      structureScore: number | null;
      materialScore: number | null;
      packagingScore: number | null;
      mark: string | null;
      title: string;
      description: string;
      imageFileName: string;
      imageURL: string;
      location: string;
      latitude: number | null;
      longitude: number | null;
      createdAt: Date;
      authorId: string;
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 缺少参数或无效的卡片类型
  - `403`: 只有作者可以修改卡片信息
  - `404`: 卡片不存在
  - `500`: 服务器错误

---

# 道具交互系统 API 文档

## 概述

道具交互系统允许用户之间发送虚拟道具，支持创建交互、撤回交互、修改已读状态等功能。

---

## API 接口列表

### 1. 创建道具交互

- **接口路径**: `POST /api/prop-interaction`
- **功能**: 创建新的道具交互记录
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/prop-interaction" \
  -H "content-type: application/json" \
  -d '{"senderUserId":"alice","receiverUserId":"bob","propId":1,"remark":"生日快乐！"}'
```

- **请求参数**:

  ```typescript
  // Body 参数
  {
    senderUserId: string; // 发送道具的用户ID
    receiverUserId: string; // 接收道具的用户ID
    propId: number; // 道具ID，前端用于调用本地动画
    remark?: string; // 可选：发送者备注信息
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "道具交互创建成功";
    data: {
      id: string;
      senderUserId: string;
      receiverUserId: string;
      propId: number;
      remark: string | null;
      interactionTime: Date;
      receivedTime: Date | null;
      isRead: boolean;
      sender: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
      receiver: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
    }
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 参数错误或不能向自己发送道具
  - `404`: 发送者或接收者不存在
  - `500`: 服务器错误

---

### 2. 撤回道具交互

- **接口路径**: `DELETE /api/prop-interaction?interactionId={id}&userId={userId}`
- **功能**: 撤回道具交互（仅限对方未读状态，直接删除记录）
- **测试性 Curl：**

```bash
curl -i -X DELETE "http://*************:3000/api/prop-interaction?interactionId=clxxxxx123&userId=alice"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    interactionId: string; // 交互记录ID（String类型）
    userId: string; // 发送者用户ID
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "交互撤回成功";
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 参数错误或对方已查看无法撤回
  - `403`: 只有发送者可以撤回交互
  - `404`: 交互记录不存在
  - `500`: 服务器错误

---

### 3. 修改已读状态

- **接口路径**: `PATCH /api/prop-interaction`
- **功能**: 修改道具交互的已读状态（需验证传入的 userId 是否等于接收者的 id）
- **测试性 Curl：**

```bash
curl -i -X PATCH "http://*************:3000/api/prop-interaction" \
  -H "content-type: application/json" \
  -d '{"interactionId":"clxxxxx123","userId":"bob","isRead":true}'
```

- **请求参数**:

  ```typescript
  // Body 参数
  {
    interactionId: string; // 交互记录ID（String类型）
    userId: string; // 接收者用户ID
    isRead: boolean; // 是否已读
    receivedTime?: string; // 可选：接收时间（ISO字符串）
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "已读状态更新成功";
    data: {
      id: string;
      senderUserId: string;
      receiverUserId: string;
      propId: number;
      remark: string | null;
      interactionTime: Date;
      receivedTime: Date | null;
      isRead: boolean;
      sender: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
      receiver: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
    }
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 参数错误
  - `403`: 只有接收者可以修改已读状态
  - `404`: 交互记录不存在
  - `500`: 服务器错误

---

### 4. 查询发送统计

- **接口路径**: `GET /api/prop-interaction/stats?userId={userId}`
- **功能**: 查询指定用户发送给其他用户的已读情况，按照已读和未读分组返回
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/prop-interaction/stats?userId=alice"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string; // 发送者用户ID
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "查询成功";
    data: {
      stats: {
        total: number; // 总发送数量
        read: number; // 已读数量
        unread: number; // 未读数量
      }
      readInteractions: Array<{
        id: string;
        senderUserId: string;
        receiverUserId: string;
        propId: number;
        interactionTime: Date;
        receivedTime: Date | null;
        isRead: boolean;
        receiver: {
          userId: string;
          nickname: string;
          avatarURL: string | null;
        };
      }>;
      unreadInteractions: Array<{
        id: string;
        senderUserId: string;
        receiverUserId: string;
        propId: number;
        interactionTime: Date;
        receivedTime: Date | null;
        isRead: boolean;
        receiver: {
          userId: string;
          nickname: string;
          avatarURL: string | null;
        };
      }>;
    }
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

### 5. 查询未读信息

- **接口路径：** `GET /api/prop-interaction?userId={userId}`

- **功能**： 查询用户个人还未读的记录

请求参数：

```typescript
// Query 参数
{
  userId: string; // 查询者用户ID
}
```

响应参数：

```typescript
{
    "success": true,
    "message": "查询成功",
    "data": [
        {
            "id": "cmf6lovrs0003qg4zydov3hgt",
            "senderUserId": "ffy6511",
            "receiverUserId": "demodemo",
            "propId": 1,
            "remark": "我很快就到！",
            "interactionTime": "2025-09-05T08:56:15.000Z",
            "receivedTime": null,
            "isRead": false,
            "sender": {
                "userId": "ffy6511",
                "nickname": "ffy",
                "avatarURL": "https://carboncoin-cos-1358266118.cos.ap-shanghai.myqcloud.com/images/1756460346199_q7woknyb40h.png"
            }
        }
    ]
}
```

---

## 数据模型

### PropInteraction 模型

```typescript
{
  id: string; // CUID主键（与项目其他模型保持一致）
  senderUserId: string; // 发送道具的用户ID
  receiverUserId: string; // 接收道具的用户ID
  propId: number; // 道具ID，前端用于调用本地动画
  interactionTime: Date; // 交互发生时间
  receivedTime: Date | null; // 接收时间（触发动画的时间）
  isRead: boolean; // 用户B是否已查看
  remark: String?; // 发送者携带的可选备注信息，用于前端显示
}
```

## 注意事项

1. **ID 类型**: PropInteraction 模型使用 String 类型的 CUID 主键，与项目中其他模型保持一致
2. **道具信息管理**: 道具信息由前端本地管理，后端只存储 propId 作为标识符
3. **权限验证**:
   - 只有发送者可以撤回交互（且仅限对方未读状态）
   - 只有接收者可以修改已读状态
4. **时间处理**:
   - 如果标记为已读且之前没有接收时间，系统会自动设置当前时间
   - 可以手动指定接收时间
5. **数据一致性**: 所有操作都包含完整的参数验证和错误处理

---

# 用户日志系统 API 文档

## 概述

用户日志系统用于记录用户的各种活动，包括地点打卡(location)、出行足迹(trip)和卡片识别(recognition)三种类型的记录。

---

## API 接口列表

### 1. 查询用户日志

- **接口路径**: `GET /api/user-logs?userId={userId}&recordType={recordType}&isPublic={isPublic}&startDate={startDate}&endDate={endDate}&page={page}&limit={limit}`
- **功能**: 查询用户的日志记录，支持分页和筛选
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/user-logs?userId=alice&recordType=recognition&page=1&limit=10"
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必填）
    recordType?: "location" | "trip" | "recognition"; // 记录类型（可选）
    isPublic?: boolean; // 是否公开（可选）
    startDate?: string; // 开始日期（可选，ISO格式）
    endDate?: string; // 结束日期（可选，ISO格式）
    page?: number; // 页码，默认1
    limit?: number; // 每页数量，默认20，最大100
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      logs: Array<{
        id: string;
        userId: string;
        recordType: "location" | "trip" | "recognition";
        recordId: string;
        imageList: string[] | null;
        description: string | null;
        isPublic: boolean;
        createdAt: Date;
        updatedAt: Date;
        user: {
          userId: string;
          nickname: string;
          avatarURL: string | null;
        };
        likes: Array<{
          id: string;
          userId: string;
          createdAt: Date;
          user: {
            userId: string;
            nickname: string;
            avatarURL: string | null;
          };
        }>;
        comments: Array<{
          id: string;
          userId: string;
          content: string;
          replyTo: string | null;
          createdAt: Date;
          user: {
            userId: string;
            nickname: string;
            avatarURL: string | null;
          };
        }>;
        // 根据recordType不同，会包含不同的关联数据
        LocationCheckIns?: {
          id: string;
          position: string | null;
          latitude: number;
          longitude: number;
          createdAt: Date;
        } | null;
        UserFootprints?: {
          id: string;
          footPrints: any; // JSON数组
          activityType: "walking" | "cycling" | "bus" | "subway";
          isFinished: boolean;
          totalDistance: number;
          createdAt: Date;
        } | null;
        ItemCard?: {
          id: string;
          title: string;
          description: string;
          cardType: "scenery" | "shopping";
          themeColor: string | null;
          coinReward: number;
          experienceReward: number;
          imageURL: string;
          location: string;
          createdAt: Date;
        } | null;
        // 对于recognition类型的日志，会额外包含CardAcquisitionRecord
        CardAcquisitionRecord?: {
          id: string;
          userId: string;
          cardId: string;
          acquiredAt: Date;
          isAuthor: boolean;
          card: {
            id: string;
            title: string;
            description: string;
            cardType: "scenery" | "shopping";
            themeColor: string | null;
            coinReward: number;
            experienceReward: number;
            imageURL: string;
            location: string;
            latitude: number | null;
            longitude: number | null;
            createdAt: Date;
            author: {
              userId: string;
              nickname: string;
              avatarURL: string | null;
            };
          };
        } | null;
      }>;
      pagination: {
        current: number; // 当前页码
        total: number; // 总页数
        count: number; // 总记录数
        limit: number; // 每页数量
        hasNext: boolean; // 是否有下一页
        hasPrev: boolean; // 是否有上一页
      }
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

### 重要说明

**对于 recognition 类型的日志记录**：

- 除了返回关联的`ItemCard`信息外，还会额外返回`CardAcquisitionRecord`字段
- `CardAcquisitionRecord`包含用户获得该卡片的详细记录，包括获得时间、是否为作者等信息
- `CardAcquisitionRecord.card`包含完整的卡片信息和作者信息
- 这样前端可以同时获得卡片的基本信息和用户的获得记录信息

---

# 地点打卡与出行记录交互系统 API 文档

## 概述

地点打卡与出行记录交互系统支持用户在出行过程中进行地点打卡，地点打卡会自动关联到当前进行中的出行记录。支持多张照片上传和描述信息。

---

## API 接口列表

### 1. 创建地点打卡记录（增强版）

- **接口路径**: `POST /api/location-checkins`
- **功能**: 创建新的地点打卡记录，支持多张照片和描述，自动关联到进行中的出行记录
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/location-checkins" \
  -H "content-type: application/json" \
  -d '{"userId":"alice","position":"星巴克","latitude":39.9042,"longitude":116.4074,"photoURLs":["https://example.com/photo1.jpg","https://example.com/photo2.jpg"],"description":"在星巴克休息，喝了一杯拿铁"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 用户ID（必需）
    position?: string; // 地点名称，如"星巴克"（可选，最大100字符）
    latitude: number; // 纬度（必需，-90到90之间）
    longitude: number; // 经度（必需，-180到180之间）
    photoURLs?: string[]; // 多张照片的URL数组（可选，最多10张）
    description?: string; // 用户输入的描述（可选，最大500字符）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "地点打卡记录创建成功";
    data: {
      id: string;
      userId: string;
      position: string | null;
      latitude: number;
      longitude: number;
      photoURLs: string[] | null; // 照片URL数组
      description: string | null; // 用户描述
      userFootprintsId: string | null; // 关联的出行记录ID
      createdAt: Date;
      updatedAt: Date;
    }
  }
  ```
- **状态码**:
  - `201`: 创建成功
  - `400`: 参数错误或验证失败
  - `404`: 用户不存在
  - `500`: 服务器错误

**特殊功能**：

- 系统会自动查找用户当前进行中的出行记录（`isFinished=false`）
- 如果找到进行中的出行记录，地点打卡会自动关联到该出行记录
- 如果没有进行中的出行记录，地点打卡会作为独立记录保存
- 自动创建对应的用户日志记录，包含照片和描述信息

### 2. 查询地点打卡记录（增强版）

- **接口路径**: `GET /api/location-checkins?userId={userId}&startDate={startDate}&endDate={endDate}`
- **功能**: 查询用户的地点打卡记录，包含关联的出行记录信息
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/location-checkins?userId=alice"
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必需）
    startDate?: string; // 开始日期（可选，ISO格式）
    endDate?: string; // 结束日期（可选，ISO格式）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "查询成功";
    data: Array<{
      id: string;
      userId: string;
      position: string | null;
      latitude: number;
      longitude: number;
      photoURLs: string[] | null; // 照片URL数组
      description: string | null; // 用户描述
      userFootprintsId: string | null; // 关联的出行记录ID
      createdAt: Date;
      updatedAt: Date;
      UserFootprints: {
        id: string;
        activityType: "walking" | "cycling" | "bus" | "subway";
        isFinished: boolean;
        totalDistance: number;
        createdAt: Date;
      } | null; // 关联的出行记录信息
    }>;
  }
  ```
- **状态码**:
  - `200`: 查询成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

### 3. 查询地点打卡详情（增强版）

- **接口路径**: `POST /api/location-checkins/detail`
- **功能**: 查询指定地点打卡记录的详细信息，包含用户信息和关联的出行记录
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/location-checkins/detail" \
  -H "content-type: application/json" \
  -d '{"id":"clxxx123"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    id: string; // 地点打卡记录ID（必需）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "查询成功";
    data: {
      id: string;
      userId: string;
      position: string | null;
      latitude: number;
      longitude: number;
      photoURLs: string[] | null; // 照片URL数组
      description: string | null; // 用户描述
      userFootprintsId: string | null; // 关联的出行记录ID
      createdAt: Date;
      updatedAt: Date;
      user: {
        userId: string;
        nickname: string;
        avatar: string | null;
        avatarURL: string | null;
      };
      UserFootprints: {
        id: string;
        activityType: "walking" | "cycling" | "bus" | "subway";
        isFinished: boolean;
        totalDistance: number;
        createdAt: Date;
      } | null; // 关联的出行记录信息
    }
  }
  ```
- **状态码**:
  - `200`: 查询成功
  - `404`: 记录不存在
  - `500`: 服务器错误

### 4. 查询出行记录详情（增强版）

- **接口路径**: `POST /api/footprints/detail`
- **功能**: 查询指定出行记录的详细信息，包含关联的所有地点打卡记录
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/footprints/detail" \
  -H "content-type: application/json" \
  -d '{"footprintId":"clxxx123"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    footprintId: string; // 出行记录ID（必需）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      id: string;
      userId: string;
      footPrints: any; // JSON数组，包含轨迹点
      activityType: "walking" | "cycling" | "bus" | "subway";
      isFinished: boolean;
      totalDistance: number;
      createdAt: Date;
      updatedAt: Date;
      user: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
      locationCheckIns: Array<{
        id: string;
        position: string | null;
        latitude: number;
        longitude: number;
        photoURLs: string[] | null; // 照片URL数组
        description: string | null; // 用户描述
        createdAt: Date;
        updatedAt: Date;
      }>; // 关联的地点打卡记录，按时间顺序排列
    }
  }
  ```
- **状态码**:
  - `200`: 查询成功
  - `400`: 参数错误
  - `404`: 记录不存在
  - `500`: 服务器错误

### 5. 查询出行记录列表（增强版）

- **接口路径**: `GET /api/footprints?userId={userId}&startDate={startDate}&endDate={endDate}&activityType={activityType}`
- **功能**: 查询用户的出行记录列表，包含关联的地点打卡记录
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/footprints?userId=alice"
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必需）
    startDate?: string; // 开始日期（可选，ISO格式）
    endDate?: string; // 结束日期（可选，ISO格式）
    activityType?: "walking" | "cycling" | "bus" | "subway"; // 活动类型（可选）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: Array<{
      id: string;
      userId: string;
      footPrints: any; // JSON数组，包含轨迹点
      activityType: "walking" | "cycling" | "bus" | "subway";
      isFinished: boolean;
      totalDistance: number;
      createdAt: Date;
      updatedAt: Date;
      locationCheckIns: Array<{
        id: string;
        position: string | null;
        latitude: number;
        longitude: number;
        photoURLs: string[] | null; // 照片URL数组
        description: string | null; // 用户描述
        createdAt: Date;
      }>; // 关联的地点打卡记录，按时间顺序排列
    }>;
  }
  ```
- **状态码**:
  - `200`: 查询成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

## 数据模型变更

### LocationCheckIns 模型（增强版）

```typescript
{
  id: string; // 主键
  userId: string; // 用户ID
  position: string | null; // 地点名称
  latitude: number; // 纬度
  longitude: number; // 经度
  photoURLs: string[] | null; // 多张照片的URL数组（新增）
  description: string | null; // 用户输入的描述（新增）
  userFootprintsId: string | null; // 关联的出行记录ID（新增）
  createdAt: Date; // 创建时间
  updatedAt: Date; // 更新时间
}
```

### UserFootprints 模型（增强版）

```typescript
{
  id: string; // 主键
  userId: string; // 用户ID
  footPrints: any; // JSON数组，包含轨迹点
  activityType: "walking" | "cycling" | "bus" | "subway"; // 活动类型
  isFinished: boolean; // 是否已完成此次出行
  totalDistance: number; // 总距离（公里）
  createdAt: Date; // 创建时间
  updatedAt: Date; // 更新时间
  locationCheckIns: LocationCheckIns[]; // 关联的地点打卡记录（新增）
}
```

## 重要特性

1. **自动关联机制**：

   - 创建地点打卡时，系统自动查找用户当前进行中的出行记录
   - 如果存在 `isFinished=false` 的出行记录，地点打卡会自动关联
   - 支持一次出行包含多个地点打卡

2. **多媒体支持**：

   - 支持上传多张照片（最多 10 张）
   - 支持添加文字描述（最多 500 字符）
   - 照片和描述会同步到用户日志系统

3. **数据完整性**：

   - 所有查询 API 都返回完整的关联信息
   - 出行记录包含其下的所有地点打卡
   - 地点打卡包含其关联的出行记录信息

4. **向后兼容**：
   - 新增字段都是可选的，不影响现有功能
   - 独立的地点打卡（未关联出行记录）仍然正常工作

---

# 卡片奖励计算系统

## 概述

卡片系统现在支持基于评分的自动奖励计算。前端提交评分数据，后端自动计算碳币和经验奖励。

## 奖励计算公式

### 碳币计算公式

```
carbonCoins = Math.round((structureScore * 0.3 + materialScore * 0.4 + packagingScore * 0.3) * 0.1) + (mark ? 5 : 0)
```

**权重分配：**

- 结构评分权重：30%
- 材料评分权重：40%
- 包装评分权重：30%
- 加权和乘以 0.1 后四舍五入
- 如果 mark 字段非空（非 null 且非空字符串），额外奖励 5 碳币

### 经验计算公式

```
experience = Math.round((structureScore + materialScore + packagingScore) * 0.2)
```

**计算方式：**

- 三个评分相加后乘以 0.2，然后四舍五入

## 评分验证规则

- 所有评分字段都是可选的（可以为 null 或 undefined）
- 如果提供评分，必须是 0-100 之间的数字
- mark 字段为可选字符串，必须是有效的环保标志才会提供额外碳币奖励

## 有效的环保标志

系统只接受以下环保标志，其他值将被忽略：

- `ResinID` - 树脂识别码
- `MobiusLoop` - Mobius 循环标志
- `FSC` - FSC 森林认证
- `GreenDot` - 绿色点标志
- `WEEE` - WEEE 电器回收
- `BatteryRecycle` - 电池回收标志
- `Compostable` - 可堆肥认证
- `MetalGlassRecycle` - 金属/玻璃回收

**注意**: 如果传入的 mark 不在上述列表中，系统会自动将其设置为 null，不会获得额外的 5 碳币奖励。

## 示例计算

**示例 1：**

- structureScore: 85
- materialScore: 90
- packagingScore: 80
- mark: "FSC"

计算过程：

- 加权分数：85 _ 0.3 + 90 _ 0.4 + 80 \* 0.3 = 25.5 + 36 + 24 = 85.5
- 基础碳币：Math.round(85.5 \* 0.1) = Math.round(8.55) = 9
- mark 奖励：5 碳币
- 总碳币：9 + 5 = 14
- 经验：Math.round((85 + 90 + 80) \* 0.2) = Math.round(51) = 51

**示例 2：**

- structureScore: 70
- materialScore: null (默认为 0)
- packagingScore: 60
- mark: null

计算过程：

- 加权分数：70 _ 0.3 + 0 _ 0.4 + 60 \* 0.3 = 21 + 0 + 18 = 39
- 基础碳币：Math.round(39 \* 0.1) = Math.round(3.9) = 4
- mark 奖励：0 碳币
- 总碳币：4
- 经验：Math.round((70 + 0 + 60) \* 0.2) = Math.round(26) = 26

---

# 生态数据统计系统 API 文档

## 概述

生态数据统计系统为前端提供用户环保活动的聚合数据，支持统计分析、连续记录、最值记录、结构分析等功能。所有时间计算基于北京时间(Asia/Shanghai)。

---

## API 接口列表

### 1. 统计数据区域

- **接口路径**: `GET /api/eco/summary?userId={userId}&month={month}`
- **功能**: 获取用户的总体统计数据和指定月份的详细数据
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/eco/summary?userId=alice&month=2024-10"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必填）
    month?: string; // 月份，格式YYYY-MM（可选，默认当前月份）
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      userId: string;
      totalActiveDays: number; // 总活跃天数
      totalLogs: {
        overall: number; // 总日志数
      }
      monthly: {
        month: string; // YYYY-MM
        logsBreakdown: {
          overall: number; // 当月总日志数
          trip: number; // 出行日志数
          checkin: number; // 地点打卡日志数
          card: number; // 卡片识别日志数
        }
        carbon: {
          valueKg: number; // 当月总减碳量(kg)
          equivalents: {
            trees: number; // 相当于多少棵树一年的固碳量
            bulbHours: number; // 相当于多少小时60W灯泡用电
            subwayKm: number; // 相当于多少公里地铁出行
          }
        }
      }
    }
  }
  ```

- **状态码**:
  - `200`: 成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

### 2. 连续记录区域

- **接口路径**: `GET /api/eco/streaks?userId={userId}`
- **功能**: 获取用户的连续记录数据（同时完成三项打卡的连续天数）
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/eco/streaks?userId=alice"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必填）
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      userId: string;
      currentAll3: number; // 当前连续"同时完成三项"的天数
      maxAll3: number; // 历史最长连续"同时完成三项"的天数
      longestSingle: {
        peek: {
          type: "trip" | "checkin" | "card"; // 最长连续记录的类型
          value: number; // 最长连续天数
        }
        detail: {
          trip: number; // 出行最长连续天数
          checkin: number; // 地点打卡最长连续天数
          card: number; // 卡片创建最长连续天数
        }
      }
    }
  }
  ```

- **状态码**:
  - `200`: 成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

### 3. 记录最值区域

- **接口路径**: `GET /api/eco/highlights?userId={userId}&month={month}`
- **功能**: 获取指定月份的最佳记录（最高购物卡得分和最高出行减碳量）
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/eco/highlights?userId=alice&month=2024-10"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必填）
    month?: string; // 月份，格式YYYY-MM（可选，默认当前月份）
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      userId: string;
      month: string; // YYYY-MM
      bestCard?: {
        cardId: string; // 卡片ID
        title: string; // 卡片标题
        score: number; // 平均得分
        logId?: string; // 关联的日志ID（可选）
      };
      bestTrip?: {
        userFootprintsId: string; // 出行记录ID
        title: string; // 出行描述
        carbonSaved: number; // 减碳量(kg)
        logId?: string; // 关联的日志ID（可选）
      };
    }
  }
  ```

- **状态码**:
  - `200`: 成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

### 4. 结构组成区域

- **接口路径**: `GET /api/eco/structure?userId={userId}&month={month}`
- **功能**: 获取指定月份的结构组成数据（出行方式、卡片类型、购物卡分数分布）
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/eco/structure?userId=alice&month=2024-10"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必填）
    month?: string; // 月份，格式YYYY-MM（可选，默认当前月份）
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      userId: string;
      month: string; // YYYY-MM
      transport: {
        counts: {
          walking: number; // 步行次数
          cycling: number; // 骑行次数
          bus: number; // 公交次数
          subway: number; // 地铁次数
        }
        ratio: {
          walking: number; // 步行占比 (0-1)
          cycling: number; // 骑行占比 (0-1)
          bus: number; // 公交占比 (0-1)
          subway: number; // 地铁占比 (0-1)
        }
      }
      cards: {
        counts: {
          scenery: number; // 风景卡片数量
          shopping: number; // 购物卡片数量
        }
        ratio: {
          scenery: number; // 风景卡片占比 (0-1)
          shopping: number; // 购物卡片占比 (0-1)
        }
      }
      shoppingScoreDist: {
        counts: {
          low: number; // 低分区间[0,60)数量
          mid: number; // 中分区间[60,80)数量
          high: number; // 高分区间[80,100]数量
        }
        ratio: {
          low: number; // 低分区间占比 (0-1)
          mid: number; // 中分区间占比 (0-1)
          high: number; // 高分区间占比 (0-1)
        }
      }
    }
  }
  ```

- **状态码**:
  - `200`: 成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

### 5. 日历数据

- **接口路径**: `GET /api/eco/calendar?userId={userId}&month={month}`
- **功能**: 获取指定月份的每日生态数据，用于日历展示和跳转
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/eco/calendar?userId=alice&month=2024-10"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必填）
    month?: string; // 月份，格式YYYY-MM（可选，默认当前月份）
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      userId: string;
      month: string; // YYYY-MM
      days: Array<{
        date: string; // YYYY-MM-DD
        hasCard: boolean; // 当日是否创建卡片
        hasCheckin: boolean; // 当日是否地点打卡
        hasFootprint: boolean; // 当日是否完成出行
        co2SavedKg: number; // 当日减碳量(kg)
        counts: {
          logs: number; // 当日日志总数
          trip: number; // 当日出行记录数
          checkin: number; // 当日地点打卡数
          card: number; // 当日卡片创建数
        };
      }>;
    }
  }
  ```

- **状态码**:
  - `200`: 成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

## 重要特性

### 1. 时间处理

- 所有日期计算基于北京时间(Asia/Shanghai)
- 月份参数格式为 YYYY-MM
- 日期边界按北京时间的自然日计算

### 2. 数据聚合

- 使用 UserEcoDaily 表进行日常数据预聚合
- 支持实时计算和缓存优化
- 连续记录基于每日聚合数据计算

### 3. 等价事件计算

- 1kg CO2 = 0.05 棵树一年固碳量
- 1kg CO2 = 50 小时 60W 灯泡用电
- 1kg CO2 = 5 公里地铁出行

### 4. 评分计算

- 购物卡平均分 = (结构评分 + 材料评分 + 包装评分) / 3
- 出行减碳量基于距离和交通方式系数计算
- 分数区间：低[0,60)、中[60,80)、高[80,100]

### 5. 连续记录规则

- "同时完成三项"指当日同时有卡片创建、地点打卡、出行完成
- 连续记录要求日期连续，中断则重新计算
- 支持当前连续和历史最长连续的区分
